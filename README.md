# ReepResent - Presentation Management Dashboard

ReepResent adalah aplikasi dashboard modern untuk manajemen presentasi yang dibangun dengan Next.js 15, TypeScript, Tailwind CSS, dan shadcn/ui.

## 🚀 Fitur

- **Dashboard Modern**: Interface yang clean dan responsif dengan sidebar yang dapat dikollapse
- **Manajemen Presentasi**: <PERSON><PERSON><PERSON> semua presentasi Anda dalam satu tempat
- **Analytics**: Lihat statistik dan performa presentasi
- **Media Library**: Kelola file media (gambar, video, dokumen, audio)
- **Settings**: Konfigurasi profil dan pengaturan akun
- **Responsive Design**: Bekerja sempurna di desktop dan mobile

## 🛠️ Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **UI Components**: shadcn/ui
- **Icons**: Lucide React
- **Font**: <PERSON><PERSON><PERSON> & Geist Mono

## 📋 Prerequisites

Pastikan Anda telah menginstall:

- Node.js (versi 18 atau lebih baru)
- npm, yarn, pnpm, atau bun

## 🚀 Instalasi dan Menjalankan Project

### 1. Clone Repository

```bash
git clone <repository-url>
cd reepresent
```

### 2. Install Dependencies

```bash
npm install
# atau
yarn install
# atau
pnpm install
```

### 3. Jalankan Development Server

```bash
npm run dev
# atau
yarn dev
# atau
pnpm dev
```

### 4. Buka Browser

Buka [http://localhost:3000](http://localhost:3000) untuk melihat aplikasi.

## 📁 Struktur Project

```
reepresent/
├── app/                    # App Router pages
│   ├── analytics/         # Halaman analytics
│   ├── dashboard/         # Halaman dashboard (alternatif)
│   ├── presentations/     # Halaman manajemen presentasi
│   ├── settings/          # Halaman pengaturan
│   ├── layout.tsx         # Root layout
│   └── page.tsx          # Homepage dengan dashboard
├── components/            # Reusable components
│   ├── ui/               # shadcn/ui components
│   ├── app-sidebar.tsx   # Main sidebar component
│   ├── nav-main.tsx      # Main navigation
│   ├── nav-projects.tsx  # Projects navigation
│   ├── nav-user.tsx      # User menu
│   └── team-switcher.tsx # Team switcher
├── hooks/                # Custom hooks
├── lib/                  # Utility functions
└── public/              # Static assets
```

## 🎨 Komponen UI

Project ini menggunakan shadcn/ui dengan komponen-komponen berikut:

- Sidebar dengan collapsible functionality
- Breadcrumb navigation
- Button, Input, Avatar
- Dropdown Menu, Tooltip
- Separator, Sheet, Skeleton
- Collapsible components

## 🔧 Kustomisasi

### Mengubah Data Sidebar

Edit file `components/app-sidebar.tsx` untuk mengubah:

- Menu navigasi utama
- Daftar projects
- Informasi user
- Team switcher

### Menambah Halaman Baru

1. Buat folder baru di `app/`
2. Tambahkan `page.tsx` dengan struktur yang sama
3. Update navigasi di `app-sidebar.tsx`

### Styling

- Gunakan Tailwind CSS classes
- Kustomisasi theme di `app/globals.css`
- Komponen menggunakan CSS variables untuk theming

## 📱 Responsive Design

Dashboard secara otomatis menyesuaikan dengan ukuran layar:

- **Desktop**: Sidebar penuh dengan semua fitur
- **Tablet**: Sidebar dapat dikollapse
- **Mobile**: Sidebar menjadi overlay

## 🚀 Build untuk Production

```bash
npm run build
npm start
```

## 📝 Scripts yang Tersedia

- `npm run dev` - Menjalankan development server
- `npm run build` - Build aplikasi untuk production
- `npm run start` - Menjalankan production server
- `npm run lint` - Menjalankan ESLint

## 🤝 Contributing

1. Fork repository
2. Buat feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push ke branch (`git push origin feature/AmazingFeature`)
5. Buat Pull Request

## 📄 License

Project ini menggunakan MIT License.

## 🆘 Troubleshooting

### Port sudah digunakan

Jika port 3000 sudah digunakan, Next.js akan otomatis menggunakan port berikutnya (3001, 3002, dst).

### Error saat install dependencies

Coba hapus `node_modules` dan `package-lock.json`, lalu install ulang:

```bash
rm -rf node_modules package-lock.json
npm install
```

### Build error

Pastikan semua dependencies sudah terinstall dan tidak ada TypeScript errors:

```bash
npm run lint
npx tsc --noEmit
```
