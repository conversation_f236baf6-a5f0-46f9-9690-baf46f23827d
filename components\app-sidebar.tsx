"use client";

import * as React from "react";
import {
  AudioWaveform,
  BookOpen,
  Bot,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
  Settings2,
  SquareTerminal,
} from "lucide-react";

import { NavMain } from "@/components/nav-main";
import { NavProjects } from "@/components/nav-projects";
import { NavUser } from "@/components/nav-user";
import { TeamSwitcher } from "@/components/team-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";

// This is sample data.
const data = {
  user: {
    name: "Admin",
    email: "<EMAIL>",
    avatar: "/avatars/admin.jpg",
  },
  teams: [
    {
      name: "ReepResent",
      logo: GalleryVerticalEnd,
      plan: "Pro",
    },
    {
      name: "Development",
      logo: AudioWaveform,
      plan: "Team",
    },
    {
      name: "Production",
      logo: Command,
      plan: "Enterprise",
    },
  ],
  navMain: [
    {
      title: "Dashboard",
      url: "/",
      icon: SquareTerminal,
      isActive: true,
      items: [
        {
          title: "Overview",
          url: "/",
        },
        {
          title: "Analytics",
          url: "/analytics",
        },
        {
          title: "Reports",
          url: "/reports",
        },
      ],
    },
    {
      title: "Presentations",
      url: "#",
      icon: Bot,
      items: [
        {
          title: "All Presentations",
          url: "/presentations",
        },
        {
          title: "Templates",
          url: "/templates",
        },
        {
          title: "Shared",
          url: "/shared",
        },
      ],
    },
    {
      title: "Media Library",
      url: "#",
      icon: BookOpen,
      items: [
        {
          title: "Images",
          url: "/media/images",
        },
        {
          title: "Videos",
          url: "/media/videos",
        },
        {
          title: "Documents",
          url: "/media/documents",
        },
        {
          title: "Audio",
          url: "/media/audio",
        },
      ],
    },
    {
      title: "Settings",
      url: "#",
      icon: Settings2,
      items: [
        {
          title: "Profile",
          url: "/settings/profile",
        },
        {
          title: "Account",
          url: "/settings/account",
        },
        {
          title: "Preferences",
          url: "/settings/preferences",
        },
        {
          title: "Security",
          url: "/settings/security",
        },
      ],
    },
  ],
  projects: [
    {
      name: "Marketing Campaign",
      url: "/projects/marketing",
      icon: Frame,
    },
    {
      name: "Sales Presentation",
      url: "/projects/sales",
      icon: PieChart,
    },
    {
      name: "Product Launch",
      url: "/projects/product",
      icon: Map,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
