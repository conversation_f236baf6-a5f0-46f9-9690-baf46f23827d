"use client";

import * as React from "react";
import {
  AudioWaveform,
  BookOpen,
  Bot,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
  Settings2,
  SquareTerminal,
  Home,
  User,
  Briefcase,
  FolderOpen,
  Users,
  Palette,
  PenTool,
  Code,
  ShoppingCart,
  Library,
} from "lucide-react";

import { NavMain } from "@/components/nav-main";
import { NavProjects } from "@/components/nav-projects";
import { NavUser } from "@/components/nav-user";
import { TeamSwitcher } from "@/components/team-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";

// This is sample data.
const data = {
  user: {
    name: "Admin",
    email: "<EMAIL>",
    avatar: "/avatars/admin.jpg",
  },
  teams: [
    {
      name: "ReepResent",
      logo: GalleryVerticalEnd,
      plan: "Pro",
    },
    {
      name: "Development",
      logo: AudioWaveform,
      plan: "Team",
    },
    {
      name: "Production",
      logo: Command,
      plan: "Enterprise",
    },
  ],
  navMain: [
    {
      title: "Home",
      url: "/",
      icon: Home,
      isActive: true,
      items: [
        {
          title: "Who's ree?",
          url: "/home/<USER>",
          icon: User,
        },
        {
          title: "Experiences",
          url: "/home/<USER>",
          icon: Briefcase,
        },
        {
          title: "Projects",
          url: "/home/<USER>",
          icon: FolderOpen,
        },
      ],
    },
  ],
  projects: [
    {
      name: "Products",
      url: "#",
      icon: Frame,
      items: [
        {
          title: "reegroup",
          url: "/ree/reegroup",
          icon: Users,
        },
        {
          title: "reecreate",
          url: "/ree/reecreate",
          icon: Palette,
        },
        {
          title: "reewrite",
          url: "/ree/reewrite",
          icon: PenTool,
        },
        {
          title: "reecode",
          url: "/ree/reecode",
          icon: Code,
        },
        {
          title: "reeseller",
          url: "/ree/reeseller",
          icon: ShoppingCart,
        },
        {
          title: "libraree",
          url: "/ree/libraree",
          icon: Library,
        },
      ],
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
