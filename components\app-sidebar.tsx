"use client";

import * as React from "react";
import {
  AudioWaveform,
  BookOpen,
  Bot,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
  Settings2,
  SquareTerminal,
} from "lucide-react";

import { NavMain } from "@/components/nav-main";
import { NavProjects } from "@/components/nav-projects";
import { NavUser } from "@/components/nav-user";
import { TeamSwitcher } from "@/components/team-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";

// This is sample data.
const data = {
  user: {
    name: "Admin",
    email: "<EMAIL>",
    avatar: "/avatars/admin.jpg",
  },
  teams: [
    {
      name: "ReepResent",
      logo: GalleryVerticalEnd,
      plan: "Pro",
    },
    {
      name: "Development",
      logo: AudioWaveform,
      plan: "Team",
    },
    {
      name: "Production",
      logo: Command,
      plan: "Enterprise",
    },
  ],
  navMain: [
    {
      title: "Home",
      url: "/",
      icon: SquareTerminal,
      isActive: true,
      items: [
        {
          title: "Who's ree?",
          url: "/home/<USER>",
        },
        {
          title: "Experiences",
          url: "/home/<USER>",
        },
        {
          title: "Projects",
          url: "/home/<USER>",
        },
      ],
    },
  ],
  projects: [
    {
      name: "Marketing Campaign",
      url: "/projects/marketing",
      icon: Frame,
    },
    {
      name: "Sales Presentation",
      url: "/projects/sales",
      icon: PieChart,
    },
    {
      name: "Product Launch",
      url: "/projects/product",
      icon: Map,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
