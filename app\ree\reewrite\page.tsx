import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";

export default function ReeWritePage() {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="#">Ree Ecosystem</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>reewrite</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <div className="bg-muted/50 min-h-[100vh] flex-1 rounded-xl md:min-h-min">
            <div className="p-8">
              <div className="max-w-4xl mx-auto">
                <div className="text-center mb-8">
                  <div className="w-20 h-20 bg-primary/20 rounded-xl mx-auto mb-4 flex items-center justify-center">
                    <span className="text-3xl">✍️</span>
                  </div>
                  <h1 className="text-4xl font-bold mb-4">reewrite</h1>
                  <p className="text-xl text-muted-foreground">
                    Advanced writing platform with AI-powered assistance
                  </p>
                </div>
                
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
                  <div className="bg-background rounded-lg p-6 border">
                    <h3 className="text-lg font-semibold mb-3">AI Writing Assistant</h3>
                    <p className="text-muted-foreground text-sm">
                      Get intelligent suggestions and improvements for your writing with advanced AI technology.
                    </p>
                  </div>
                  <div className="bg-background rounded-lg p-6 border">
                    <h3 className="text-lg font-semibold mb-3">Content Management</h3>
                    <p className="text-muted-foreground text-sm">
                      Organize and manage your documents with powerful content management features.
                    </p>
                  </div>
                  <div className="bg-background rounded-lg p-6 border">
                    <h3 className="text-lg font-semibold mb-3">Collaboration</h3>
                    <p className="text-muted-foreground text-sm">
                      Share and collaborate on documents with team members in real-time.
                    </p>
                  </div>
                </div>
                
                <div className="text-center">
                  <Button size="lg" className="mr-4">
                    Start Writing
                  </Button>
                  <Button size="lg" variant="outline">
                    View Templates
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
